import 'package:flutter/material.dart';
import 'package:otlop_app/models/product_model.dart';
import 'package:otlop_app/providers/order_provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:otlop_app/screens/cart/cart_screen.dart';
import 'package:otlop_app/services/api_service.dart';

class ProductDetailScreen extends StatefulWidget {
  final Product product;
  final String? restaurantId;

  const ProductDetailScreen({
    Key? key,
    required this.product,
    this.restaurantId,
  }) : super(key: key);

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}


class _ProductDetailScreenState extends State<ProductDetailScreen> {
  int _quantity = 1;
  final Map<int, bool> _selectedIngredients = {};
  bool _isInCart = false;
  bool _isCheckingCart = true;
  bool _isFromDifferentRestaurant = false;
  String? _currentRestaurantName;

  // State variables for ingredient fetching
  List<Ingredient>? _ingredients;
  bool _isLoadingIngredients = false;
  final ApiService _apiService = ApiService();

  @override
  void initState() {
    super.initState();

    // Initialize selected ingredients if available from the passed product
    if (widget.product.ingredients != null) {
      for (final ingredient in widget.product.ingredients!) {
        // All ingredients start as unselected (customer can choose what they want)
        _selectedIngredients[ingredient.id] = false;
      }
    }

    // Fetch fresh ingredients from the backend
    _fetchIngredients();

    // Check if product is already in cart
    _checkIfProductInCart();
  }

  // Fetch ingredients from the backend
  Future<void> _fetchIngredients() async {
    try {
      print('Starting to fetch ingredients for product ID: ${widget.product.id}');
      setState(() {
        _isLoadingIngredients = true;
      });

      final productData = await _apiService.getProductDetails(widget.product.id);
      print('Received product data: ${productData.keys.join(', ')}');

      if (productData['Ingredients'] != null) {
        final List<dynamic> ingredientsJson = productData['Ingredients'];
        print('Found ${ingredientsJson.length} ingredients in response');

        _ingredients = ingredientsJson.map((json) {
          print('Processing ingredient: $json');
          return Ingredient.fromJson(json);
        }).toList();

        // Initialize selected ingredients for the fetched ingredients
        for (final ingredient in _ingredients!) {
          if (!_selectedIngredients.containsKey(ingredient.id)) {
            _selectedIngredients[ingredient.id] = false;
          }
        }

        print('Successfully fetched ${_ingredients!.length} ingredients from backend');
      } else {
        print('No ingredients found in product data');
      }

      setState(() {
        _isLoadingIngredients = false;
      });
    } catch (e) {
      print('Error fetching ingredients: $e');
      setState(() {
        _isLoadingIngredients = false;
      });
      // Continue with the ingredients from the original product if available
    }
  }

  // Check if the product is already in the cart and if it's from a different restaurant
  Future<void> _checkIfProductInCart() async {
    setState(() {
      _isCheckingCart = true;
    });

    try {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);

      // Check if product is already in cart
      final isInCart = await orderProvider.isProductInCart(widget.product.id);

      // Check if product is from a different restaurant
      final restaurantId = widget.product.restaurantId ??
          (widget.restaurantId != null ? int.parse(widget.restaurantId!) : 0);
      final isDifferentRestaurant = await orderProvider.isFromDifferentRestaurant(restaurantId);

      // Get current restaurant in cart (if any)
      final currentRestaurant = await orderProvider.getCurrentRestaurantInCart();
      final currentRestaurantName = currentRestaurant?['name'] as String?;

      if (mounted) {
        setState(() {
          _isInCart = isInCart;
          _isFromDifferentRestaurant = isDifferentRestaurant;
          _currentRestaurantName = currentRestaurantName;
          _isCheckingCart = false;
        });
      }
    } catch (e) {
      debugPrint('Error checking if product is in cart: $e');
      if (mounted) {
        setState(() {
          _isCheckingCart = false;
        });
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh cart status when dependencies change (e.g., when returning to this screen)
    _checkIfProductInCart();
  }

  @override
  Widget build(BuildContext context) {
    final primaryColor = Theme.of(context).colorScheme.primary;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode ? Colors.grey[900] : Colors.white;
    final textColor = isDarkMode ? Colors.white : Colors.black;

    // Use the original product data
    final currentProduct = widget.product;

    return Scaffold(
      backgroundColor: backgroundColor,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.favorite_border),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('تمت إضافة ${currentProduct.name} إلى المفضلة'),
                  duration: const Duration(seconds: 1),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Product image
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.35,
            width: double.infinity,
            child: Hero(
              tag: 'product_image_${widget.product.id}',
              child: currentProduct.image != null && currentProduct.image!.isNotEmpty
                ? CachedNetworkImage(
                    imageUrl: currentProduct.image!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[200],
                      child: const Center(
                        child: Icon(
                          Icons.restaurant,
                          size: 50,
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  )
                : Container(
                    color: Colors.grey[200],
                    child: const Center(
                      child: Icon(
                        Icons.restaurant,
                        size: 50,
                        color: Colors.grey,
                      ),
                    ),
                  ),
            ),
          ),

          // Product details
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // No loading indicator needed since we use the passed product data

                  // Product name and availability
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          currentProduct.name,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            fontFamily: 'Alx',
                            color: textColor,
                          ),
                        ),
                      ),
                      if (currentProduct.available)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green.withAlpha(30),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'متوفر',
                            style: TextStyle(
                              color: Colors.green,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )
                      else
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red.withAlpha(30),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Text(
                            'غير متوفر',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Price
                  Text(
                    '${currentProduct.price.toStringAsFixed(2)} د.ل',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: primaryColor,
                    ),
                  ),

                  const Divider(height: 32),

                  // Restaurant info (if available)
                  if (currentProduct.restaurantName != null) ...[
                    Row(
                      children: [
                        Icon(
                          Icons.restaurant,
                          size: 18,
                          color: textColor.withAlpha(150),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          currentProduct.restaurantName!,
                          style: TextStyle(
                            fontSize: 16,
                            color: textColor.withAlpha(200),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Description (if available)
                  if (currentProduct.description != null && currentProduct.description!.isNotEmpty) ...[
                    Text(
                      'الوصف',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: textColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      currentProduct.description!,
                      style: TextStyle(
                        fontSize: 16,
                        height: 1.5,
                        color: textColor.withAlpha(200),
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Divider before ingredients
                  const Divider(height: 32),

                  // Ingredients section with enhanced visibility
                  _buildIngredientsSection(textColor),

                  // Quantity selector
                  Text(
                    'الكمية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: textColor,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.remove_circle_outline),
                        onPressed: _quantity > 1 ? _decrementQuantity : null,
                        color: primaryColor,
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          _quantity.toString(),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: textColor,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.add_circle_outline),
                        onPressed: _incrementQuantity,
                        color: primaryColor,
                      ),
                    ],
                  ),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildSimpleAddToCartBar(),
    );
  }

  // Build ingredients section with loading state
  Widget _buildIngredientsSection(Color textColor) {
    // Determine which ingredients to show
    List<Ingredient>? ingredientsToShow;

    if (_ingredients != null && _ingredients!.isNotEmpty) {
      // Use fetched ingredients from backend
      ingredientsToShow = _ingredients;
      print('Using fetched ingredients: ${_ingredients!.length}');
    } else if (widget.product.ingredients != null && widget.product.ingredients!.isNotEmpty) {
      // Fallback to original product ingredients
      ingredientsToShow = widget.product.ingredients;
      print('Using original product ingredients: ${widget.product.ingredients!.length}');
    } else {
      // For testing - create some sample ingredients if none exist
      print('No ingredients found, creating sample ingredients for testing');
      ingredientsToShow = [
        Ingredient(id: 1, name: 'زيتون', price: null, optional: true),
        Ingredient(id: 2, name: 'جبنة', price: null, optional: true),
        Ingredient(id: 3, name: 'طماطم', price: null, optional: true),
      ];

      // Initialize selected ingredients for sample ingredients
      for (final ingredient in ingredientsToShow) {
        if (!_selectedIngredients.containsKey(ingredient.id)) {
          _selectedIngredients[ingredient.id] = false;
        }
      }
    }

    // Always show the ingredients section for testing
    print('Showing ingredients section with ${ingredientsToShow?.length ?? 0} ingredients');

    if (ingredientsToShow == null || ingredientsToShow.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withAlpha(10),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withAlpha(30),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Simple Header
          Row(
            children: [
              Icon(
                Icons.restaurant_menu,
                color: Theme.of(context).colorScheme.primary,
                size: 18,
              ),
              const SizedBox(width: 8),
              Text(
                'المكونات المتاحة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: textColor,
                  fontFamily: 'Alx',
                ),
              ),
              if (_isLoadingIngredients) ...[
                const SizedBox(width: 8),
                const SizedBox(
                  width: 14,
                  height: 14,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),

          // Ingredients List
          ...ingredientsToShow.map((ingredient) => _buildSimpleIngredientItem(ingredient)),
        ],
      ),
    );
  }

  // Clean and simple ingredient item
  Widget _buildSimpleIngredientItem(Ingredient ingredient) {
    final isSelected = _selectedIngredients[ingredient.id] ?? false;
    final textColor = Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.black;
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedIngredients[ingredient.id] = !isSelected;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected ? primaryColor.withAlpha(20) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? primaryColor : Colors.grey.withAlpha(50),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Simple checkbox
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: isSelected ? primaryColor : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(
                    color: isSelected ? primaryColor : Colors.grey,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 14,
                      )
                    : null,
              ),
              const SizedBox(width: 12),

              // Ingredient name
              Expanded(
                child: Text(
                  ingredient.name,
                  style: TextStyle(
                    fontSize: 15,
                    color: textColor,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Simple add to cart bar
  Widget _buildSimpleAddToCartBar() {
    final subtotal = _calculateSubtotal();
    final primaryColor = Theme.of(context).colorScheme.primary;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 4,
            offset: Offset(0, -1),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Price
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'السعر الإجمالي',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                Text(
                  '${subtotal.toStringAsFixed(2)} د.ل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: primaryColor,
                  ),
                ),
              ],
            ),

            const SizedBox(width: 16),

            // Add to cart button
            Expanded(
              child: _isCheckingCart
                  ? const Center(child: CircularProgressIndicator())
                  : Tooltip(
                      message: _isFromDifferentRestaurant && _currentRestaurantName != null
                          ? 'لديك منتجات من $_currentRestaurantName في السلة'
                          : '',
                      textAlign: TextAlign.center,
                      verticalOffset: -60,
                      showDuration: const Duration(seconds: 3),
                      child: ElevatedButton(
                      onPressed: _isInCart || _isFromDifferentRestaurant
                          ? null
                          : (widget.product.available ? _addToCart : null),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isInCart || _isFromDifferentRestaurant
                            ? Colors.grey[300]
                            : primaryColor,
                        foregroundColor: Colors.white,
                        disabledBackgroundColor: Colors.grey[300],
                        disabledForegroundColor: Colors.grey[600],
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        _isInCart
                            ? 'موجود في السلة'
                            : _isFromDifferentRestaurant
                                ? 'لا يمكن إضافة منتجات من مطاعم مختلفة'
                                : 'أضف إلى السلة',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: _isFromDifferentRestaurant ? 14 : 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
            ),
          ],
        ),
      ),
    );
  }



  void _incrementQuantity() {
    setState(() {
      _quantity++;
    });
  }

  void _decrementQuantity() {
    if (_quantity > 1) {
      setState(() {
        _quantity--;
      });
    }
  }

  double _calculateSubtotal() {
    // Base price (only original price, no discount)
    double basePrice = widget.product.price;

    // Add ingredients price - use fetched ingredients if available
    double ingredientsPrice = 0;
    List<Ingredient>? ingredientsToUse = _ingredients ?? widget.product.ingredients;

    if (ingredientsToUse != null) {
      for (final ingredient in ingredientsToUse) {
        if (_selectedIngredients[ingredient.id] == true && ingredient.price != null) {
          ingredientsPrice += ingredient.price!;
        }
      }
    }

    // Total price for all quantities
    return (basePrice + ingredientsPrice) * _quantity;
  }

  void _addToCart() {
    // Show error if product is unavailable
    if (!widget.product.available) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.error_outline, color: Colors.white),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'هذا المنتج غير متوفر حاليا',
                  style: TextStyle(
                    fontFamily: 'Alx',
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red.shade700,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          duration: const Duration(seconds: 2),
        ),
      );
      return;
    }

    // Get selected ingredients with improved error handling - use fetched ingredients if available
    final List<Map<String, dynamic>> selectedIngredientsList = [];
    List<Ingredient>? ingredientsToUse = _ingredients ?? widget.product.ingredients;

    if (ingredientsToUse != null) {
      for (final ingredient in ingredientsToUse) {
        if (_selectedIngredients[ingredient.id] == true) {
          selectedIngredientsList.add({
            'ingredientId': ingredient.id,
            'name': ingredient.name,
            'price': ingredient.price,
          });
        }
      }
    }

    // Create cart item with proper validation and all necessary product details
    final cartItem = {
      'productId': widget.product.id,
      'productName': widget.product.name,
      'price': widget.product.price,
      'quantity': _quantity.clamp(1, 99), // Ensure quantity is within reasonable limits
      'restaurantId': widget.product.restaurantId ?? (widget.restaurantId != null ? int.parse(widget.restaurantId!) : 0),
      'restaurantName': widget.product.restaurantName ?? 'Restaurant',
      'productImage': widget.product.image,
      'selectedIngredients': selectedIngredientsList,
    };

    // Store product name for later use
    final productName = widget.product.name;
    final primaryColor = Theme.of(context).colorScheme.primary;

    // Show a brief loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    // Get the order provider
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);

    // Add to cart (async operation)
    debugPrint('DEBUG: Adding to cart: $cartItem');
    orderProvider.addToCartApi(cartItem).then((_) {
      debugPrint('DEBUG: Successfully added to cart');
      // Success case
      if (mounted) {
        // Dismiss loading indicator
        Navigator.pop(context);

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(30),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check_circle_outline,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'تمت الإضافة إلى السلة',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          fontFamily: 'Alx',
                        ),
                      ),
                      Text(
                        '$productName × $_quantity',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            backgroundColor: primaryColor,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            duration: const Duration(seconds: 3),
            action: SnackBarAction(
              label: 'عرض السلة',
              textColor: Colors.white,
              onPressed: () {
                // Navigate to cart screen
                if (mounted) {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) { // Re-check mounted as it's an async callback
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CartScreen(),
                        ),
                      );
                    }
                  });
                }
              },
            ),
          ),
        );

        // Navigate back to previous screen with a slight delay for better UX
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            Navigator.pop(context);
          }
        });
      }
    }).catchError((e) {
      // Error case
      debugPrint('ERROR: Failed to add to cart: $e');
      if (mounted) {
        // Dismiss loading indicator
        Navigator.pop(context);

        // Check if it's a different restaurant error
        final errorMessage = e.toString();
        final isDifferentRestaurantError = errorMessage.contains('different restaurants');

        // Show appropriate error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    isDifferentRestaurantError
                        ? 'لا يمكن إضافة منتجات من مطاعم مختلفة. يرجى إفراغ السلة أولاً.'
                        : 'حدث خطأ أثناء إضافة المنتج إلى السلة',
                    style: const TextStyle(
                      fontFamily: 'Alx',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red.shade700,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            duration: const Duration(seconds: 4),
            action: isDifferentRestaurantError
                ? SnackBarAction(
                    label: 'إفراغ السلة',
                    textColor: Colors.white,
                    onPressed: () {
                      // Clear the cart
                      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
                      orderProvider.clearCart().then((_) {
                        // Try adding the product again after clearing the cart
                        _addToCart();
                      });
                    },
                  )
                : null,
          ),
        );
      }
    });
  }
}