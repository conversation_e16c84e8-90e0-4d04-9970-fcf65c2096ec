# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 68ms
  [gap of 31ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 129ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 72ms
  [gap of 36ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 140ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 38ms]
  create-invalidation-state 88ms
  [gap of 36ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 178ms

