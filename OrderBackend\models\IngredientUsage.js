const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('IngredientUsage', {
    UsageID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    ProductCartID: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'ProductCart',
        key: 'ProductCartID'
      }
    },
    IngredientID: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Ingredient',
        key: 'IngredientID'
      }
    },
    IsNeeded: {
      type: DataTypes.BOOLEAN,
      allowNull: false
    }
  }, {
    sequelize,
    tableName: 'IngredientUsage',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__Ingredie__29B197C0A57A6106",
        unique: true,
        fields: [
          { name: "UsageID" },
        ]
      },
    ]
  });
};
