class Product {
  final int id;
  final String name;
  final String? description;
  final double price;
  final String? image;
  final bool available;
  final int? restaurantId;
  final String? restaurantName;
  final int? categoryId;
  final String? categoryName;
  final List<Ingredient>? ingredients;
  final double? discountPrice;
  final int? discountPercentage;

  Product({
    required this.id,
    required this.name,
    this.description,
    required this.price,
    this.image,
    required this.available,
    this.restaurantId,
    this.restaurantName,
    this.categoryId,
    this.categoryName,
    this.ingredients,
    this.discountPrice,
    this.discountPercentage,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    // Handle different ID formats
    int parseId(dynamic id) {
      if (id == null) return 0;
      if (id is int) return id;
      try {
        return int.parse(id.toString());
      } catch (e) {
        return 0;
      }
    }

    // Handle different price formats
    double parsePrice(dynamic price) {
      if (price == null) return 0.0;
      if (price is double) return price;
      if (price is int) return price.toDouble();
      try {
        return double.parse(price.toString());
      } catch (e) {
        return 0.0;
      }
    }

    // Parse ingredients if available
    List<Ingredient>? parseIngredients(dynamic ingredientsData) {
      if (ingredientsData == null) return null;

      try {
        if (ingredientsData is List) {
          return List<Ingredient>.from(
            ingredientsData.map((x) => Ingredient.fromJson(x))
          );
        }
      } catch (e) {
        print('Error parsing ingredients: $e');
      }

      return null;
    }

    return Product(
      id: parseId(json['id'] ?? json['ProductID']),
      name: json['name'] ?? json['ProductName'] ?? '',
      description: json['description'] ?? json['Description'] ?? json['Details'],
      price: parsePrice(json['price'] ?? json['Price']),
      image: json['image'] ?? json['Image'] ?? json['ImageUrl'],
      available: json['available'] ?? json['AvailabilityStatus'] ?? true,
      restaurantId: parseId(json['restaurantId'] ?? json['RestaurantID']),
      restaurantName: json['restaurantName'] ?? json['RestaurantName'],
      categoryId: parseId(json['categoryId'] ?? json['CategoryID']),
      categoryName: json['categoryName'] ?? json['CategoryName'],
      ingredients: parseIngredients(json['ingredients'] ?? json['Ingredients']),
      discountPrice: parsePrice(json['discountPrice']),
      discountPercentage: json['discountPercentage'] != null ?
          int.tryParse(json['discountPercentage'].toString()) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'image': image,
      'available': available,
      'restaurantId': restaurantId,
      'restaurantName': restaurantName,
      'categoryId': categoryId,
      'categoryName': categoryName,
      'ingredients': ingredients?.map((x) => x.toJson()).toList(),
      'discountPrice': discountPrice,
      'discountPercentage': discountPercentage,
    };
  }
}

class Ingredient {
  final int id;
  final String name;
  final double? price;
  final bool? optional;

  Ingredient({
    required this.id,
    required this.name,
    this.price,
    this.optional,
  });

  factory Ingredient.fromJson(Map<String, dynamic> json) {
    // Parse ID from different formats
    int parseId(dynamic id) {
      if (id == null) return 0;
      if (id is int) return id;
      try {
        return int.parse(id.toString());
      } catch (e) {
        return 0;
      }
    }

    // Parse price from different formats
    double? parsePrice(dynamic price) {
      if (price == null) return null;
      if (price is double) return price;
      if (price is int) return price.toDouble();
      try {
        return double.parse(price.toString());
      } catch (e) {
        return null;
      }
    }

    return Ingredient(
      id: parseId(json['id'] ?? json['IngredientID']),
      name: json['name'] ?? json['IngredientName'] ?? '',
      price: parsePrice(json['price'] ?? json['Price']),
      optional: json['optional'] ?? true, // Default to true so all ingredients are selectable
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'optional': optional,
    };
  }
}

class CartItem {
  final int id;
  final int productId;
  final String productName;
  final double price;
  final int quantity;
  final String? productImage;
  final int restaurantId;
  final String restaurantName;
  final List<SelectedIngredient>? selectedIngredients;

  CartItem({
    required this.id,
    required this.productId,
    required this.productName,
    required this.price,
    required this.quantity,
    this.productImage,
    required this.restaurantId,
    required this.restaurantName,
    this.selectedIngredients,
  });

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'],
      productId: json['productId'],
      productName: json['productName'],
      price: json['price'].toDouble(),
      quantity: json['quantity'],
      productImage: json['productImage'],
      restaurantId: json['restaurantId'],
      restaurantName: json['restaurantName'],
      selectedIngredients: json['selectedIngredients'] != null
          ? List<SelectedIngredient>.from(
              json['selectedIngredients'].map((x) => SelectedIngredient.fromJson(x)))
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'productId': productId,
      'productName': productName,
      'price': price,
      'quantity': quantity,
      'productImage': productImage,
      'restaurantId': restaurantId,
      'restaurantName': restaurantName,
      'selectedIngredients': selectedIngredients?.map((x) => x.toJson()).toList(),
    };
  }
}

class SelectedIngredient {
  final int id;
  final int ingredientId;
  final String name;
  final double? price;

  SelectedIngredient({
    required this.id,
    required this.ingredientId,
    required this.name,
    this.price,
  });

  factory SelectedIngredient.fromJson(Map<String, dynamic> json) {
    return SelectedIngredient(
      id: json['id'],
      ingredientId: json['ingredientId'],
      name: json['name'],
      price: json['price']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ingredientId': ingredientId,
      'name': name,
      'price': price,
    };
  }
}