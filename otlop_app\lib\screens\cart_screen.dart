import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/order_provider.dart';
import '../models/product_model.dart' as models;
import '../services/cart_storage_service.dart';
import '../services/api_service.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  bool _isLoading = false;
  String? _error;
  final CartStorageService _cartStorageService = CartStorageService();

  List<models.CartItem> _cartItems = [];

  @override
  void initState() {
    super.initState();
    // Initialize cart storage
    _initCartStorage();
  }

  Future<void> _initCartStorage() async {
    print('DEBUG: Initializing cart storage in CartScreen');
    await _cartStorageService.init();
    // Load cart items when the screen initializes
    _loadCartItems();
  }

  Future<void> _loadCartItems() async {
    if (!mounted) return;

    print('DEBUG: Loading cart items in CartScreen');
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load directly from storage
      final items = await _cartStorageService.getCartItems();

      // Update state with loaded items
      if (mounted) {
        setState(() {
          _cartItems = items;
        });
      }

      // Debug info
      print('DEBUG: CartScreen loaded ${items.length} items directly from storage');
      for (var item in items) {
        print('DEBUG: Cart item: id=${item.id}, name=${item.productName}, price=${item.price}, quantity=${item.quantity}');
      }

      // Also update the provider (optional)
      if (mounted) {
        final orderProvider = Provider.of<OrderProvider>(context, listen: false);
        await orderProvider.fetchCartItems();
      }
    } catch (e) {
      print('ERROR: Failed to load cart items in CartScreen: ${e.toString()}');
      if (mounted) {
        setState(() {
          _error = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Helper method to fix image URLs
  String _getImageUrl(String imageUrl) {
    if (imageUrl.isEmpty) return '';
    if (imageUrl.startsWith('http')) return imageUrl;

    // Remove leading slash if present to avoid double slashes
    String cleanUrl = imageUrl.startsWith('/') ? imageUrl.substring(1) : imageUrl;

    // Simply combine base URL with the image path from API
    return '${ApiService.baseUrl}/$cleanUrl';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'الـسـلـة',
          style: TextStyle(
            color: Colors.white,
            fontSize: 24,
            fontFamily: 'Alx',
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        actions: [
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCartItems,
          ),
        ],
      ),
      body: _buildCartContent(),
    );
  }

  Widget _buildCartContent() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $_error',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _loadCartItems,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_cartItems.isEmpty) {
      return const Center(
        child: Text(
          'Your cart is empty',
          style: TextStyle(fontSize: 18),
        ),
      );
    }

    // If we have items, show them
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _cartItems.length,
            itemBuilder: (ctx, index) {
              final item = _cartItems[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product image
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: item.productImage != null && item.productImage!.isNotEmpty
                          ? Image.network(
                              _getImageUrl(item.productImage!),
                              width: 80,
                              height: 80,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => Container(
                                width: 80,
                                height: 80,
                                color: Colors.grey[300],
                                child: const Icon(Icons.fastfood, color: Colors.grey),
                              ),
                            )
                          : Container(
                              width: 80,
                              height: 80,
                              color: Colors.grey[300],
                              child: const Icon(Icons.fastfood, color: Colors.grey),
                            ),
                      ),

                      const SizedBox(width: 12),

                      // Product details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.productName,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              item.restaurantName,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  '${item.price.toStringAsFixed(2)} د.ل',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                                Row(
                                  children: [
                                    IconButton(
                                      icon: const Icon(Icons.remove_circle_outline),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                      onPressed: () {
                                        if (item.quantity > 1) {
                                          _updateCartItemQuantity(item.id.toString(), item.quantity - 1);
                                        } else {
                                          _removeCartItem(item);
                                        }
                                      },
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 8),
                                      child: Text(
                                        item.quantity.toString(),
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.add_circle_outline),
                                      padding: EdgeInsets.zero,
                                      constraints: const BoxConstraints(),
                                      onPressed: () {
                                        _updateCartItemQuantity(item.id.toString(), item.quantity + 1);
                                      },
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Delete button
                      IconButton(
                        icon: const Icon(Icons.delete_outline, color: Colors.red),
                        onPressed: () => _removeCartItem(item),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),

        // Checkout bar
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(20),
                blurRadius: 8,
                offset: const Offset(0, -4),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Total
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'المجموع',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${_calculateTotal(_cartItems).toStringAsFixed(2)} د.ل',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Checkout button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _checkout(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'إتمام الطلب',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );


  }

  // Update cart item quantity
  Future<void> _updateCartItemQuantity(String cartItemId, int newQuantity) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Convert string ID to int
      final int id = int.parse(cartItemId);

      // Update in local storage
      await _cartStorageService.updateCartItemQuantity(id, newQuantity);

      // Reload cart items
      await _loadCartItems();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating quantity: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Remove item from cart
  Future<void> _removeCartItem(models.CartItem item) async {
    // Show confirmation dialog
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إزالة العنصر'),
        content: Text('هل أنت متأكد من إزالة ${item.productName} من السلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('إزالة'),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Remove from local storage
      await _cartStorageService.removeCartItem(item.id);

      // Update local state
      setState(() {
        _cartItems.removeWhere((cartItem) => cartItem.id == item.id);
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error removing item: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Calculate total price of cart items
  double _calculateTotal(List<models.CartItem> items) {
    return items.fold<double>(
      0,
      (total, item) => total + (item.price * item.quantity)
    );
  }

  // Handle checkout process
  Future<void> _checkout(BuildContext context) async {
    // Store context reference before async operations
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);

    try {
      // Show loading indicator
      setState(() {
        _isLoading = true;
      });

      // Create order from cart
      // Note: This method needs to be implemented in OrderProvider
      // For now, we'll just simulate a successful order
      await Future.delayed(const Duration(seconds: 2));

      // Clear cart items
      await _cartStorageService.clearCart();

      // Also clear the in-memory cart
      setState(() {
        _cartItems = [];
      });

      // Show success message if still mounted
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('Order placed successfully!'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back to home
        navigator.pop();
      }
    } catch (e) {
      // Show error message if still mounted
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Failed to place order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Hide loading indicator if still mounted
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
