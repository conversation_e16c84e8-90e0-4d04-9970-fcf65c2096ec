import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ApiService {
  static const String baseUrl = 'http://**************:3001'; // For Android Emulator
  // static const String baseUrl = 'http://localhost:3000/api'; // For iOS Simulator or web

  // Cache for access token
  String? _accessToken;

  // Getter for access token
  String? get accessToken => _accessToken;

  // Constructor - initialize by loading token from SharedPreferences
  ApiService() {
    _loadToken();
  }

  // Load token from SharedPreferences

  Future<void> _loadToken() async {
    final prefs = await SharedPreferences.getInstance();
    _accessToken = prefs.getString('access_token');
  }

  // Headers with authorization
  Future<Map<String, String>> _getHeaders() async {
    Map<String, String> headers = {
      'Content-Type': 'application/json',
    };

    if (_accessToken != null) {
      headers['Authorization'] = 'Bearer $_accessToken';
    } else {
      // Try to get token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('access_token');
      if (token != null) {
        _accessToken = token;
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  // Set access token
  Future<void> setAccessToken(String token) async {
    _accessToken = token;
    // Save to shared preferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('access_token', token);
    print('Access token set and saved to SharedPreferences: ${token.substring(0, min(10, token.length))}...');
  }

  // Helper to get the minimum of two integers
  int min(int a, int b) => a < b ? a : b;

  // Clear access token on logout
  Future<void> clearAccessToken() async {
    _accessToken = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('access_token');
    print('Access token cleared from SharedPreferences');
  }

  // GET request with retry mechanism
  Future<dynamic> get(String endpoint, {int retries = 1}) async {
    final headers = await _getHeaders();

    for (int attempt = 0; attempt <= retries; attempt++) {
      try {
        final response = await http.get(
          Uri.parse('$baseUrl/$endpoint'),
          headers: headers,
        );

        return _handleResponse(response);
      } catch (e) {
        print('Network error on attempt ${attempt + 1}/${retries + 1}: $e');

        // If this was the last attempt, rethrow the error
        if (attempt == retries) {
          rethrow;
        }

        // Wait before retrying
        await Future.delayed(Duration(milliseconds: 500 * (attempt + 1)));
      }
    }

    // This should never be reached due to the rethrow above
    throw Exception('Failed to complete request after $retries retries');
  }

  // POST request
  Future<dynamic> post(String endpoint, {Map<String, dynamic>? data}) async {
    final headers = await _getHeaders();
    final response = await http.post(
      Uri.parse('$baseUrl/$endpoint'),
      headers: headers,
      body: data != null ? jsonEncode(data) : null,
    );

    return _handleResponse(response);
  }

  // PUT request
  Future<dynamic> put(String endpoint, Map<String, dynamic> data) async {
    final headers = await _getHeaders();
    final response = await http.put(
      Uri.parse('$baseUrl/$endpoint'),
      headers: headers,
      body: jsonEncode(data),
    );

    return _handleResponse(response);
  }

  // DELETE request
  Future<dynamic> delete(String endpoint) async {
    final headers = await _getHeaders();
    final response = await http.delete(
      Uri.parse('$baseUrl/$endpoint'),
      headers: headers,
    );

    return _handleResponse(response);
  }

  // Get product details with ingredients
  Future<Map<String, dynamic>> getProductDetails(int productId) async {
    try {
      final response = await get('products/$productId');

      if (response['success'] == true && response['data'] != null) {
        final productData = response['data'];

        // Ensure image URL is properly formatted
        if (productData['Image'] != null && !productData['Image'].toString().startsWith('http')) {
          productData['ImageUrl'] = '$baseUrl${productData['Image']}';
        }

        return productData;
      } else {
        throw Exception('Failed to load product details: ${response['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      print('Error fetching product details: $e');
      rethrow;
    }
  }

  // Handle HTTP response
  dynamic _handleResponse(http.Response response) {
    print('API Response Status: ${response.statusCode}');
    print('API Response Body: ${response.body}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        final jsonResponse = jsonDecode(response.body);

        // Check if the response is a valid JSON object or array
        if (jsonResponse is Map<String, dynamic>) {
          // For debugging
          print('Parsed JSON object response: $jsonResponse');

          // Check for error status in the response
          if (jsonResponse['status'] == 'error') {
            print('Error status in response: ${jsonResponse['message']}');
            throw Exception(jsonResponse['message'] ?? 'Unknown error');
          }

          return jsonResponse;
        } else if (jsonResponse is List) {
          // Handle array response
          print('Parsed JSON array response with ${jsonResponse.length} items');

          // Wrap the array in a map to maintain compatibility with existing code
          return {
            'data': jsonResponse,
            'isArray': true,
          };
        } else {
          print('Invalid response format: $jsonResponse');
          throw Exception('Invalid response format');
        }
      } catch (e) {
        print('Failed to parse response: $e');
        throw Exception('Failed to parse response: ${e.toString()}');
      }
    } else if (response.statusCode == 401) {
      // Token expired or invalid
      print('Unauthorized: Clearing access token');
      // Don't await here to avoid blocking the error propagation
      clearAccessToken().then((_) => print('Token cleared after 401 error'));
      throw Exception('Unauthorized: Please log in again');
    } else {
      // Try to parse error message
      try {
        final errorResponse = jsonDecode(response.body);
        if (errorResponse is Map<String, dynamic> && errorResponse.containsKey('message')) {
          print('Error message from server: ${errorResponse['message']}');
          throw Exception(errorResponse['message']);
        } else {
          print('Unknown error occurred: $errorResponse');
          throw Exception('Unknown error occurred');
        }
      } catch (e) {
        print('Error parsing error response: $e');
        throw Exception('Error ${response.statusCode}: ${response.reasonPhrase}');
      }
    }
  }
}