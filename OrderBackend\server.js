require("dotenv").config();
const express = require("express");
const cors = require("cors");
const cookieParser = require("cookie-parser");
const sequelize = require("./config/database");
const path = require("path");
const { authenticateToken } = require("./middleware/authMiddleware");
const checkPermission = require("./middleware/permissionMiddleware");
const {
  authenticateRestaurantToken,
} = require("./middleware/restaurantAuthMiddleware");
const checkRestaurantPermission = require("./middleware/restaurantPermissionMiddleware");
const {
  authenticateDriverToken,
} = require("./middleware/driverAuthMiddleware");
const checkDriverPermission = require("./middleware/driverPermissionMiddleware");
const authRoutes = require("./routes/authRoutes");
const {
  authenticateCustomerToken,
} = require("./middleware/customerAuthMiddleware");
const checkCustomerPermission = require("./middleware/customerPermissionMiddleware");

const restaurantAuthRoutes = require("./routes/restaurantAuthRoutes");
const driverAuthRoutes = require("./routes/driverAuthRoutes");
const customerAuthRoutes = require("./routes/customerAuthRoutes");
const restaurantRoutes = require("./routes/restaurantRoutes");
const productRoutes = require("./routes/productRoutes");
const categoryRoutes = require("./routes/categoryRoutes");
const driverRoutes = require("./routes/drivers");
const orderRoutes = require("./routes/orderRoutes");
const restaurantTypeRoutes = require("./routes/restaurantTypeRoutes");
const invoiceRoutes = require("./routes/invoices");
const homepageRoutes = require("./routes/homepage");
const moneyRoutes = require("./routes/money");
const uploadRoutes = require("./routes/uploadRoutes");
const treasuryRoutes = require("./routes/treasury");
const adminRoutes = require("./routes/adminRoutes");
const restaurantAccountRoutes = require("./routes/restaurantAccountRoutes");
const accountRoutes = require("./routes/accountRoutes");
const customerRoutes = require("./routes/customerRoutes");

const app = express();
console.log("Environment:", process.env.NODE_ENV);

// Set API_URL from env or use the default
if (!process.env.API_URL) {
  process.env.API_URL = `http://localhost:${process.env.PORT || 3001}`;
  console.log(`API_URL not set, using default: ${process.env.API_URL}`);
}

const corsOptions = {
  origin: process.env.NODE_ENV === "development"
    ? true
    : [
        "http://**************:8001",
        "http://***************:8000",
        "http://**************:9000",
        "http://***************:9000",
        "http://**************:3300",
        "http://localhost:8000",
        "http://localhost:8000/",
        "http://localhost:3000",
        "http://localhost:3000/",
        "http://localhost:9000",
        "http://localhost:9000/",

      ],
  methods: "GET,POST,PUT,DELETE,OPTIONS,PATCH",
  allowedHeaders: "Content-Type, Authorization, X-Requested-With",
  credentials: true,
  preflightContinue: false,
  optionsSuccessStatus: 204,
  exposedHeaders: ['Content-Length', 'Content-Type']
};

app.use(cors(corsOptions));
app.options("*", cors(corsOptions));

app.use((req, res, next) => {
  res.setHeader('Referrer-Policy', 'no-referrer-when-downgrade');

  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-XSS-Protection', '1; mode=block');

  res.setHeader('X-Frame-Options', 'SAMEORIGIN');

  next();
});

app.use(express.json());
app.use(cookieParser());
app.set("sequelize", sequelize);

app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.originalUrl}`);
  next();
});

app.use("/auth", authRoutes);
app.use("/restaurant-auth", restaurantAuthRoutes);
app.use("/driver-auth", driverAuthRoutes);
app.use("/customer-auth", customerAuthRoutes);

// Serve static files
app.use(
  "/uploads",
  express.static(path.join(__dirname, "public", "uploads"), {
    setHeaders: (res, path) => {
      res.set("Cross-Origin-Resource-Policy", "cross-origin");
      res.set("Access-Control-Allow-Origin", "*");
      res.set("Access-Control-Allow-Methods", "GET, OPTIONS");
      res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
    },
  })
);

app.use("/upload", uploadRoutes);

// After the static file serving middleware and before route registration
// Create default placeholder images if they don't exist
const { promises: fsPromises } = require('fs');
const createDefaultImages = async () => {
  try {
    const defaultProductImagePath = path.join(__dirname, 'public', 'uploads', 'products', 'default-product.png');
    const noImagePath = path.join(__dirname, 'public', 'uploads', 'products', 'no-image.png');

    // Check if default product image exists
    try {
      await fsPromises.access(defaultProductImagePath);
      console.log('Default product image already exists');
    } catch (err) {
      console.log('Creating default product image...');

      // Create directory if it doesn't exist
      await fsPromises.mkdir(path.join(__dirname, 'public', 'uploads', 'products'), { recursive: true });

      // Create a simple placeholder image (1x1 px transparent PNG)
      // This is a minimal base64-encoded transparent PNG
      const transparentPixel = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=', 'base64');
      await fsPromises.writeFile(defaultProductImagePath, transparentPixel);
      console.log('Default product image created');

      // Also create a no-image.png
      await fsPromises.writeFile(noImagePath, transparentPixel);
      console.log('No-image placeholder created');
    }
  } catch (error) {
    console.error('Error creating default images:', error);
  }
};

createDefaultImages();

app.use((req, res, next) => {
  // Expanded list of public paths that don't require authentication
  const publicPaths = [
    '/auth',
    '/restaurant-auth',
    '/driver-auth',
    '/customer-auth',
    '/uploads',
    '/upload',
    '/health-check',
    '/api-docs',
    '/products',  // Allow public access to product information
    '/restaurants', // Allow public access to restaurant information
    '/categories'   // Allow public access to category information
  ];

  // Skip auth for public paths and OPTIONS requests
  if (publicPaths.some(path => req.originalUrl.startsWith(path)) || req.method === 'OPTIONS') {
    console.log(`Public path accessed: ${req.originalUrl}`);
    return next();
  }

  // Check for customer auth routes - only require token for refresh-token and logout
  if (req.originalUrl.includes('customer/auth/')) {
    const requiresAuth =
      req.originalUrl.includes('/refresh-token') ||
      req.originalUrl.includes('/logout');

    if (!requiresAuth) {
      console.log('Bypassing token check for customer route:', req.originalUrl);
      return next();
    }
  }

  // At this point, we require authentication
  const authHeader = req.headers["authorization"];
  // if (!authHeader) {
  //   return res.status(401).json({
  //     success: false,
  //     message: "Token is needed"
  //   });
  // }

  try {

    const token = authHeader.split(" ")[1];
    const jwt = require("jsonwebtoken");
    const decoded = jwt.decode(token);

    if (decoded && decoded.type === "restaurant") {
      authenticateRestaurantToken(req, res, (err) => {
        if (err) return next(err);
        checkRestaurantPermission(req, res, next);
      });
    } else if (decoded && decoded.type === "driver") {
      authenticateDriverToken(req, res, (err) => {
        if (err) return next(err);
        checkDriverPermission(req, res, next);
      });
    } else if (decoded && decoded.type === "customer") {
      authenticateCustomerToken(req, res, (err) => {
        if (err) return next(err);
        checkCustomerPermission(req, res, next);
      });
    } else {
      authenticateToken(req, res, (err) => {
        if (err) return next(err);
        checkPermission(req, res, next);
      });
    }
  } catch (error) {
    console.error("Auth error:", error);
    authenticateToken(req, res, (err) => {
      if (err) return next(err);
      checkPermission(req, res, next);
    });
  }
});

app.use("/homepage", homepageRoutes);
app.use("/products", productRoutes);
app.use("/restaurants", restaurantRoutes);
app.use("/admin", adminRoutes);
app.use("/categories", categoryRoutes);
app.use("/drivers", driverRoutes);
app.use("/orders", orderRoutes);
app.use("/restaurant-types", restaurantTypeRoutes);
app.use("/invoices", invoiceRoutes);
app.use("/money", moneyRoutes);
app.use("/treasury", treasuryRoutes);
app.use("/accounts", restaurantAccountRoutes);
app.use("/accounts", accountRoutes);
app.use("/customers", customerRoutes);

app.get('/health-check', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    apiUrl: process.env.API_URL
  });
});

app.use((err, req, res, next) => {
  console.error("Server error:", err.stack);

  if (err.name === "UnauthorizedError") {
    return res.status(401).json({
      success: false,
      message: "تم رفض الوصول: يجب تسجيل الدخول - " + err.message,
      code: "UNAUTHORIZED",
    });
  }

  if (err.name === "TokenExpiredError") {
    return res.status(401).json({
      success: false,
      message: "تم رفض الوصول: انتهت صلاحية الجلسة - " + err.message,
      code: "TOKEN_EXPIRED",
    });
  }

  if (
    err.name === "CORSError" ||
    (err.message && err.message.includes("CORS"))
  ) {
    return res.status(403).json({
      success: false,
      message: "تم رفض الوصول: خطأ في سياسة مشاركة الموارد عبر الأصول (CORS)",
      error: process.env.NODE_ENV === "development" ? err.message : undefined,
      code: "CORS_ERROR",
    });
  }

  res.status(500).json({
    success: false,
    message: "حدث خطأ في الخادم",
    error: process.env.NODE_ENV === "development" ? err.message : undefined,
  });
});

app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: "لم يتم العثور على المسار المطلوب",
  });
});

const PORT = process.env.PORT || 3000;


sequelize
  .authenticate()
  .then(() => {
    console.log("Database connection has been established successfully.");

    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
    });
  })
  .catch((err) => {
    console.error("Unable to connect to the database:", err);
  });
