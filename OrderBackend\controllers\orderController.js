const { where, Op } = require('sequelize');

const db = require('../models/init-models')(require('../config/database'));
const { Order, Cart, ProductCart, Product, IngredientUsage, Ingredient, Restaurant, Customer } = db;
const { getPagination, getPagingData } = require('../utils/pagination');

// Helper function to calculate distance between two points
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
        Math.cos(φ1) * Math.cos(φ2) *
        Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
}



exports.getAllOrders = async(req, res) => {
    try {
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const orders = await Order.findAndCountAll({
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice'],
                },
                {
                    model: db.Driver,
                    as: 'Driver',
                    attributes: ['FirstName', 'LastName'],
                    required: false
                }
            ]
        });

        const formattedOrders = orders.rows.map(async order => {
            const plainOrder = order.get({ plain: true });

            const {
                StartPointLatitude,
                StartPointLongitude,
                EndPointLatitude,
                EndPointLongitude,
                ...orderData
            } = plainOrder;

            let restaurantName = null;
            let cartTotalPrice = null;
            let driverFirstName = null;
            let driverLastName = null;

            if (order.CartID) {
                const cart = await Cart.findByPk(order.CartID);
                if (cart) {
                    cartTotalPrice = cart.TotalPrice;
                    if (cart.RestaurantID) {
                        const restaurant = await Restaurant.findByPk(cart.RestaurantID);
                        if (restaurant) {
                            restaurantName = restaurant.Name;
                        }
                    }
                }
            }

            if (order.Driver) {
                driverFirstName = order.Driver.FirstName;
                driverLastName = order.Driver.LastName;
            }

            let distanceInMeters = null;
            if (
                StartPointLatitude != null &&
                StartPointLongitude != null &&
                EndPointLatitude != null &&
                EndPointLongitude != null
            ) {
                distanceInMeters = calculateDistance(
                    StartPointLatitude,
                    StartPointLongitude,
                    EndPointLatitude,
                    EndPointLongitude
                );
            }

            return {
                ...orderData,
                RestaurantName: restaurantName,
                CartTotalPrice: cartTotalPrice,
                DriverFirstName: driverFirstName,
                DriverLastName: driverLastName,
                DistanceInMeters: distanceInMeters
            };
        });

        const resolvedFormattedOrders = await Promise.all(formattedOrders);

        const response = getPagingData({ rows: resolvedFormattedOrders, count: orders.count }, page, limit);
        res.status(200).json(response);

    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Error fetching orders',
            error: error.message
        });
    }
};


exports.getOrderDetails = async(req, res) => {
    try {
        const order = await Order.findByPk(req.params.orderId, {
            include: [{
                    model: Cart,
                    as: 'Cart',
                    include: [{
                        model: ProductCart,
                        as: 'ProductCarts',
                        include: [{
                                model: Product,
                                as: 'Product',
                                attributes: ['ProductName', 'Price']
                            },
                            {
                                model: IngredientUsage,
                                as: 'IngredientUsages',
                                include: [{
                                    model: Ingredient,
                                    as: 'Ingredient',
                                    attributes: ['IngredientName']
                                }]
                            }
                        ]
                    }]
                },
                {
                    model: db.Customer,
                    as: 'Customer',
                    attributes: ['CustomerID', 'PhoneNum']
                },
                {
                    model: db.Driver,
                    as: 'Driver',
                    attributes: ['DriverID', 'FirstName', 'LastName', 'PhoneNumber']
                },
                {
                    model: db.Invoice,
                    as: 'Invoices',
                    required: false,
                    attributes: ['InvoiceID']
                }
            ]
        });

        if (!order) {
            return res.status(404).json({ message: 'Order not found' });
        }

        let restaurant = null;
        if (order.Cart) {
            restaurant = await Restaurant.findByPk(order.Cart.RestaurantID, {
                attributes: ['RestaurantID', 'Name', 'Image', 'City', 'Address']
            });
        }

        const orderData = order.get({ plain: true });

        const formattedProducts = orderData.Cart ? orderData.Cart.ProductCarts.map(pc => ({
            ProductName: pc.Product.ProductName,
            Price: pc.Product.Price,
            Quantity: pc.Quantity,
            Ingredients: pc.IngredientUsages.map(iu => iu.Ingredient.IngredientName)
        })) : [];

        const formattedOrder = {
            ...orderData,
            Cart: {
                ...orderData.Cart,
                Restaurant: restaurant,
                Products: formattedProducts
            },
            Location: {
                StartPoint: {
                    Latitude: orderData.StartPointLatitude,
                    Longitude: orderData.StartPointLongitude
                },
                EndPoint: {
                    Latitude: orderData.EndPointLatitude,
                    Longitude: orderData.EndPointLongitude
                }
            },
            DistanceInMeters: calculateDistance(
                orderData.StartPointLatitude,
                orderData.StartPointLongitude,
                orderData.EndPointLatitude,
                orderData.EndPointLongitude
            ),
            InvoiceID: order.Invoice ? order.Invoice.InvoiceID : null
        };

        delete formattedOrder.Cart.ProductCarts;
        delete formattedOrder.StartPointLatitude;
        delete formattedOrder.StartPointLongitude;
        delete formattedOrder.EndPointLatitude;
        delete formattedOrder.EndPointLongitude;

        res.json(formattedOrder);
    } catch (error) {
        console.error('Error in getOrderDetails:', error);
        res.status(500).json({ message: 'Error fetching order details' });
    }
};

exports.getOrderCart = async(req, res) => {
    try {
        const order = await Order.findByPk(req.params.orderId);
        if (!order) {
            return res.status(404).json({ message: 'لم يتم العثور على الطلب' });
        }

        const cart = await Cart.findByPk(order.CartID, {
            attributes: ['CartID', 'RestaurantID', 'CustomerID', 'TotalPrice', 'CartDate'],
            include: [{
                model: ProductCart,
                as: 'ProductCarts',
                include: [{
                        model: Product,
                        as: 'Product',
                        attributes: ['ProductName', 'Price']
                    },
                    {
                        model: IngredientUsage,
                        as: 'IngredientUsages',
                        include: [{
                            model: Ingredient,
                            as: 'Ingredient',
                            attributes: ['IngredientName']
                        }]
                    }
                ]
            }]
        });

        if (!cart) {
            return res.status(404).json({ message: 'لم يتم العثور على السلة' });
        }

        const response = {
            cartDetails: {
                cartId: cart.CartID,
                restaurantId: cart.RestaurantID,
                customerId: cart.CustomerID,
                totalPrice: cart.TotalPrice,
                cartDate: cart.CartDate
            },
            products: cart.ProductCarts.map(pc => ({
                productName: pc.Product.ProductName,
                price: pc.Product.Price,
                quantity: pc.Quantity,
                ingredients: pc.IngredientUsages.map(iu => ({
                    name: iu.Ingredient.IngredientName,
                    quantity: iu.Quantity
                }))
            }))
        };

        res.json(response);
    } catch (error) {
        console.error('Error in getOrderCart:', error);
        res.status(500).json({ message: error.message + 'حدث خطأ أثناء جلب تفاصيل السلة' });
    }
};

exports.getRestaurantOrders = async(req, res) => {
    try {
        const orders = await Order.findAll({
            include: [{
                    association: 'Driver',
                    attributes: ['FirstName', 'LastName'],
                    required: false
                },
                {
                    association: 'Cart',
                    where: { RestaurantID: req.params.restaurantId },
                    attributes: ['CartID', 'TotalPrice', 'CartDate'],
                    include: [{
                        model: Restaurant,
                        as: 'Restaurant',
                        attributes: ['Name']
                    }]
                }
            ]
        });
        res.json(orders);
    } catch (error) {
        console.error('Error in getRestaurantOrders:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء جلب طلبات المطعم' });
    }
};

exports.getDriverOrders = async(req, res) => {
    try {
        const orders = await Order.findAll({
            where: { DriverID: req.params.driverId },
            include: [{
                association: 'Cart',
                attributes: ['CartID', 'TotalPrice', 'CartDate']
            }]
        });
        res.json(orders);
    } catch (error) {
        console.error('Error in getDriverOrders:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء جلب طلبات السائق' });
    }
};

exports.editOrder = async(req, res) => {
    try {
        const order = await Order.findByPk(req.params.orderId);

        if (!order) {
            return res.status(404).json({ message: 'لم يتم العثور على الطلب' });
        }

        await order.update(req.body);

        const updatedOrder = await Order.findByPk(req.params.orderId, {
            include: [{
                association: 'Driver',
                attributes: ['FirstName', 'LastName'],
                required: false
            }]
        });

        res.json(updatedOrder);
    } catch (error) {
        console.error('Error in editOrder:', error);
        res.status(500).json({ message: 'حدث خطأ أثناء تعديل الطلب' });
    }
};

// Create a new order
exports.createOrder = async(req, res) => {
    const transaction = await db.sequelize.transaction();

    try {
        const {
            customerId,
            items,
            address,
            paymentMethod,
            note,
            endPointLatitude,
            endPointLongitude
        } = req.body;

        // Validate required fields
        if (!customerId || !items || !Array.isArray(items) || items.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Customer ID and items are required'
            });
        }

        // Get the first item to determine restaurant
        const firstItem = items[0];
        const restaurantId = firstItem.restaurantId;

        // Validate all items are from the same restaurant
        const allSameRestaurant = items.every(item => item.restaurantId === restaurantId);
        if (!allSameRestaurant) {
            return res.status(400).json({
                success: false,
                message: 'All items must be from the same restaurant'
            });
        }

        // Calculate total price
        let totalPrice = 0;
        for (const item of items) {
            totalPrice += item.price * item.quantity;
        }

        // Add delivery fee
        const deliveryFee = 5.0;
        totalPrice += deliveryFee;

        // Create cart
        const cart = await Cart.create({
            RestaurantID: restaurantId,
            CustomerID: customerId,
            TotalPrice: totalPrice,
            CartDate: new Date()
        }, { transaction });

        // Create cart items
        for (const item of items) {
            await ProductCart.create({
                CartID: cart.CartID,
                ProductID: item.productId,
                Quantity: item.quantity
            }, { transaction });

            // Handle ingredients if any
            if (item.selectedIngredients && item.selectedIngredients.length > 0) {
                for (const ingredient of item.selectedIngredients) {
                    await IngredientUsage.create({
                        ProductCartID: cart.CartID, // This might need adjustment based on your schema
                        IngredientID: ingredient.id,
                        Quantity: ingredient.quantity || 1
                    }, { transaction });
                }
            }
        }

        // Get customer location if available
        const customer = await Customer.findByPk(customerId);
        let startPointLatitude = null;
        let startPointLongitude = null;

        if (customer && customer.LocationLatitude && customer.LocationLongitude) {
            startPointLatitude = customer.LocationLatitude;
            startPointLongitude = customer.LocationLongitude;
        }

        // Create order
        const order = await Order.create({
            DriverID: null, // Will be assigned later
            CartID: cart.CartID,
            CustomerID: customerId,
            StartPointLatitude: startPointLatitude,
            StartPointLongitude: startPointLongitude,
            EndPointLatitude: endPointLatitude || null,
            EndPointLongitude: endPointLongitude || null,
            Duration: null, // Will be calculated when driver is assigned
            OrderDate: new Date(),
            Status: 0, // Pending status
            Note: note || null,
            Address: address || null,
            PaymentMethod: paymentMethod || 'cash'
        }, { transaction });

        await transaction.commit();

        // Return the created order with details
        const createdOrder = await Order.findByPk(order.OrderID, {
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    include: [
                        {
                            model: Restaurant,
                            as: 'Restaurant',
                            attributes: ['RestaurantID', 'Name', 'Image']
                        },
                        {
                            model: ProductCart,
                            as: 'ProductCarts',
                            include: [
                                {
                                    model: Product,
                                    as: 'Product',
                                    attributes: ['ProductName', 'Price', 'Image']
                                }
                            ]
                        }
                    ]
                },
                {
                    model: Customer,
                    as: 'Customer',
                    attributes: ['CustomerID', 'PhoneNum']
                }
            ]
        });

        res.status(201).json({
            success: true,
            message: 'Order created successfully',
            OrderID: order.OrderID,
            order: createdOrder
        });

    } catch (error) {
        await transaction.rollback();
        console.error('Error creating order:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create order',
            error: error.message
        });
    }
};

// Get customer orders
exports.getCustomerOrders = async(req, res) => {
    try {
        const { customerId } = req.params;
        const { page, size } = req.query;
        const { limit, offset } = getPagination(page, size);

        const orders = await Order.findAndCountAll({
            where: { CustomerID: customerId },
            limit,
            offset,
            order: [['OrderDate', 'DESC']],
            include: [
                {
                    model: Cart,
                    as: 'Cart',
                    attributes: ['TotalPrice', 'CartDate'],
                    include: [
                        {
                            model: Restaurant,
                            as: 'Restaurant',
                            attributes: ['RestaurantID', 'Name', 'Image']
                        },
                        {
                            model: ProductCart,
                            as: 'ProductCarts',
                            include: [
                                {
                                    model: Product,
                                    as: 'Product',
                                    attributes: ['ProductID', 'ProductName', 'Price', 'Image']
                                }
                            ]
                        }
                    ]
                },
                {
                    model: db.Driver,
                    as: 'Driver',
                    attributes: ['FirstName', 'LastName', 'PhoneNumber'],
                    required: false
                }
            ]
        });

        const formattedOrders = orders.rows.map(order => {
            const plainOrder = order.get({ plain: true });

            // Format products
            const products = plainOrder.Cart?.ProductCarts?.map(pc => ({
                id: pc.Product.ProductID,
                name: pc.Product.ProductName,
                price: pc.Product.Price,
                image: pc.Product.Image,
                quantity: pc.Quantity
            })) || [];

            // Calculate distance if coordinates are available
            let distanceInMeters = null;
            if (
                plainOrder.StartPointLatitude &&
                plainOrder.StartPointLongitude &&
                plainOrder.EndPointLatitude &&
                plainOrder.EndPointLongitude
            ) {
                distanceInMeters = calculateDistance(
                    plainOrder.StartPointLatitude,
                    plainOrder.StartPointLongitude,
                    plainOrder.EndPointLatitude,
                    plainOrder.EndPointLongitude
                );
            }

            return {
                OrderID: plainOrder.OrderID,
                Status: plainOrder.Status,
                OrderDate: plainOrder.OrderDate,
                TotalPrice: plainOrder.Cart?.TotalPrice || 0,
                Address: plainOrder.Address,
                PaymentMethod: plainOrder.PaymentMethod,
                Note: plainOrder.Note,
                Restaurant: {
                    id: plainOrder.Cart?.Restaurant?.RestaurantID,
                    name: plainOrder.Cart?.Restaurant?.Name,
                    image: plainOrder.Cart?.Restaurant?.Image
                },
                Driver: plainOrder.Driver ? {
                    name: `${plainOrder.Driver.FirstName} ${plainOrder.Driver.LastName}`.trim(),
                    phone: plainOrder.Driver.PhoneNumber
                } : null,
                Products: products,
                DistanceInMeters: distanceInMeters,
                Location: {
                    startPoint: {
                        latitude: plainOrder.StartPointLatitude,
                        longitude: plainOrder.StartPointLongitude
                    },
                    endPoint: {
                        latitude: plainOrder.EndPointLatitude,
                        longitude: plainOrder.EndPointLongitude
                    }
                }
            };
        });

        const response = getPagingData({
            rows: formattedOrders,
            count: orders.count
        }, page, limit);

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching customer orders:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching customer orders',
            error: error.message
        });
    }
};