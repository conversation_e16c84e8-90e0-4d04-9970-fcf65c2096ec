"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import {
  HiOutlineArrowLeft,
  HiOutlinePencilAlt,
  HiOutlineInformationCircle,
  HiOutlineTag,
  HiOutlineShoppingCart,
  HiOutlineExclamationCircle,
  HiOutlineCalendar,
  HiOutlineCheck,
  HiOutlineClock,
  HiOutlineViewList,
  HiOutlinePhotograph,
  HiOutlineExclamation,
  HiOutlineStatusOnline
} from 'react-icons/hi';

interface Ingredient {
  IngredientID: number;
  IngredientName: string;
}

interface ProductDetailsProps {
  productId: number;
}

interface ProductData {
  ProductID: number;
  ProductName: string;
  Description: string;
  Price: number;
  CategoryID: number;
  CategoryName?: string;
  Image?: string;
  ImageUrl?: string;
  Status: number;
  AvailabilityStatus: boolean;
  InsertDate: string;
  UpdateDate?: string;
  Ingredients?: Ingredient[];
}

const ProductDetails: React.FC<ProductDetailsProps> = ({ productId }) => {
  const router = useRouter();
  const [product, setProduct] = useState<ProductData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [activeImageZoom, setActiveImageZoom] = useState(false);

  // Add getImageUrl function to handle image paths consistently
  const getImageUrl = (imagePath: string | undefined) => {
    // If no image path, return default image
    if (!imagePath || imagePath === '') {
      return '/images/default-product.png';
    }
    
    // If it's already a full URL, validate and return it as is
    if (imagePath.startsWith('http')) {
      try {
        // Basic URL validation
        new URL(imagePath);
        return imagePath;
      } catch (e) {
        console.error('Invalid image URL:', imagePath);
        return '/images/default-product.png';
      }
    }
    
    // Get the base API URL, ensuring it doesn't have a trailing slash
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || '';
    const baseUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
    
    // Normalize the path to prevent duplications
    let normalizedPath = imagePath;
    
    // Remove any public/ prefix as it's not used in URLs
    normalizedPath = normalizedPath.replace(/^public\//, '');
    
    // Remove any leading slashes
    normalizedPath = normalizedPath.replace(/^\/+/, '');
    
    // Check if path already includes 'uploads/products'
    const hasUploadsProducts = normalizedPath.includes('uploads/products');
    
    // For paths that already include uploads/products, don't add it again
    if (hasUploadsProducts) {
      // Extract just the filename if there's a duplicate path
      if (normalizedPath.indexOf('uploads/products') !== normalizedPath.lastIndexOf('uploads/products')) {
        const parts = normalizedPath.split('uploads/products/');
        normalizedPath = parts[parts.length - 1];
      }
      
      // Ensure path starts with uploads/products/ prefix
      if (!normalizedPath.startsWith('uploads/products/')) {
        normalizedPath = 'uploads/products/' + normalizedPath.substring(
          normalizedPath.indexOf('uploads/products/') + 'uploads/products/'.length
        );
      }
    } else if (normalizedPath.match(/products_\d+_\d+\.webp$/i)) {
      // For product image patterns, assume they're in uploads/products
      normalizedPath = 'uploads/products/' + normalizedPath;
    } else if (!normalizedPath.includes('/')) {
      // Simple filename without path - assume it's in uploads/products
      normalizedPath = 'uploads/products/' + normalizedPath;
    }
    
    // Construct the full URL
    const fullUrl = `${baseUrl}/${normalizedPath}`;
    console.log('Generated image URL:', fullUrl);
    return fullUrl;
  };

  useEffect(() => {
    const token = localStorage.getItem('accessToken');
    setAccessToken(token);
    fetchProductDetails();
  }, [productId]);

  const fetchProductDetails = async () => {
    try {
      setLoading(true);
      const accessToken = localStorage.getItem('accessToken');
      
      if (!accessToken) {
        router.push('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/products/${productId}`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('فشل في جلب تفاصيل المنتج');
      }

      const data = await response.json();
      if (data.success) {
        // Process the data to ensure ImageUrl is set correctly
        const productData = {
          ...data.data,
          // Use the getImageUrl function to handle the image path
          ImageUrl: data.data.ImageUrl || (data.data.Image ? getImageUrl(data.data.Image) : '/images/default-product.png')
        };
        setProduct(productData);
        console.log('Product data loaded with ImageUrl:', productData.ImageUrl);
      } else {
        throw new Error(data.message || 'فشل في جلب تفاصيل المنتج');
      }
    } catch (err) {
      console.error('Error fetching product details:', err);
      setError(err instanceof Error ? err.message : 'حدث خطأ أثناء جلب تفاصيل المنتج');
    } finally {
      setLoading(false);
    }
  };

  const handleEditProduct = () => {
    router.push(`/product-management/edit/${productId}`);
  };

  const handleBackToList = () => {
    router.push('/product-management/list');
  };

  if (loading) {
    return (
      <div className="flex flex-col justify-center items-center min-h-[50vh] bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
        <div className="relative">
          <div className="h-16 w-16 rounded-full border-3 border-gray-200 border-t-gray-600 animate-spin"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"></path>
            </svg>
          </div>
        </div>
        <p className="mt-4 text-base font-medium text-gray-700 dark:text-gray-300">جاري تحميل تفاصيل المنتج...</p>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 transition-all duration-300 animate-fadeIn">
        <Alert color="failure" className="rounded-lg border-0 shadow-md">
          <div className="flex items-center">
            <HiOutlineExclamationCircle className="h-5 w-5 ml-2 text-red-600" />
            <h3 className="text-base font-medium text-red-800 dark:text-red-300">حدث خطأ</h3>
          </div>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">{error || 'لم يتم العثور على المنتج'}</p>
          <div className="mt-4">
            <button 
              onClick={handleBackToList}
              className="inline-flex items-center px-4 py-2 text-sm font-medium bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-all duration-200"
            >
              <HiOutlineArrowLeft className="ml-1.5 w-4 h-4" /> العودة إلى قائمة المنتجات
            </button>
          </div>
        </Alert>
      </div>
    );
  }

  return (
    <>
      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fadeInUp {
          animation: fadeInUp 0.6s ease-out forwards;
        }
      `}</style>

      <div className="space-y-5 pb-8 transition-all max-w-8xl mx-auto px-2 sm:px-4 animate-fadeIn">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300">
        <div className="p-4 sm:p-6">
          {/* Header with back button and edit button */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4 pb-4 border-b border-gray-100 dark:border-gray-700">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
              <span className="relative">
                {product.ProductName}
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-gray-200 dark:bg-gray-700 rounded transform translate-y-1"></span>
              </span>
            </h1>
            
            <div className="flex items-center gap-3">
              <button
                onClick={handleBackToList}
                className="inline-flex items-center px-4 py-2.5 text-sm font-medium bg-white hover:bg-gray-50 text-gray-700 border border-gray-200 rounded-lg shadow-sm hover:shadow transition-all dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200 dark:border-gray-600"
              >
                <HiOutlineArrowLeft className="ml-1.5 w-4 h-4" />
                رجوع
              </button>
              
              <button
                onClick={handleEditProduct}
                className="inline-flex items-center px-4 py-2.5 text-sm font-medium bg-blue-500 hover:bg-blue-600 text-white border border-blue-600 rounded-lg shadow-sm hover:shadow transition-all"
              >
                <HiOutlinePencilAlt className="ml-1.5 w-4 h-4" />
                تعديل المنتج
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
            {/* Left Column - Product Image */}
            <div className="lg:col-span-2">
              <div className="bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden border border-gray-100 dark:border-gray-600 shadow transition-all duration-300 hover:shadow-md">
                <div 
                  className={`relative ${activeImageZoom ? 'cursor-zoom-out' : 'cursor-zoom-in'}`}
                  onClick={() => setActiveImageZoom(!activeImageZoom)}
                >
                  <div className="aspect-w-4 aspect-h-3 relative overflow-hidden">
                    {!product.ImageUrl ? (
                      <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700">
                        <HiOutlinePhotograph className="w-16 h-16 text-gray-400" />
                      </div>
                    ) : (
                      <img
                        src={product.ImageUrl}
                        alt={product.ProductName}
                        className={`w-full h-full object-cover transition-all duration-500 ${activeImageZoom ? 'scale-125' : 'scale-100'}`}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          console.error('Image failed to load:', target.src);
                          
                          // If current src is not the default and doesn't include the API URL, try to add it
                          if (!target.src.includes('default-product.png') && 
                              !target.src.startsWith(`${process.env.NEXT_PUBLIC_API_URL}`)) {
                            console.log('Trying alternative URL format with API base URL');
                            // Try with the API base URL
                            const apiUrl = process.env.NEXT_PUBLIC_API_URL || '';
                            const baseUrl = apiUrl.endsWith('/') ? apiUrl.slice(0, -1) : apiUrl;
                            
                            // Extract the path part
                            let path = target.src;
                            if (path.includes('/uploads/')) {
                              path = path.substring(path.indexOf('/uploads/'));
                              path = path.replace(/^\/+/, ''); // Remove leading slashes
                              target.src = `${baseUrl}/${path}`;
                              console.log('Trying with URL:', target.src);
                              return; // Exit and let the browser try again with the new src
                            }
                          }
                          
                          // If still fails or can't be transformed, use fallback
                          if (!target.src.includes('default-product.png')) {
                            console.log('Using fallback image');
                            target.src = '/images/default-product.png';
                            target.classList.add('fallback-img');
                          }
                        }}
                      />
                    )}
                    
                    {/* Image overlay with zoom hint */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 flex items-end">
                      <div className="p-4 text-white w-full">
                        <div className="flex items-center justify-between">
                          <span className="text-sm bg-black/70 px-2 py-1 rounded-md">
                            {activeImageZoom ? 'انقر للتصغير' : 'انقر للتكبير'}
                          </span>
                          <span className="text-sm font-bold bg-amber-500/90 px-2 py-1 rounded-md">
                            {product.Price.toLocaleString()} دينار
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Product meta info */}
                <div className="p-4 border-t border-gray-100 dark:border-gray-600 bg-white dark:bg-gray-800">
                  <div className="flex flex-wrap gap-2">
                    <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-blue-50 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border border-blue-100 dark:border-blue-800">
                      <HiOutlineTag className="ml-1 w-3.5 h-3.5" />
                      {product.CategoryName || 'بدون فئة'}
                    </span>
                    
                    <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-amber-50 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 border border-amber-100 dark:border-amber-800">
                      <HiOutlineShoppingCart className="ml-1 w-3.5 h-3.5" />
                      {product.Price.toLocaleString()} دينار
                    </span>
                    
                    {product.Status === 1 || product.AvailabilityStatus ? (
                      <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-green-50 text-green-800 dark:bg-green-900/30 dark:text-green-300 border border-green-100 dark:border-green-800">
                        <span className="w-1.5 h-1.5 rounded-full bg-green-500 ml-1 animate-pulse"></span>
                        <HiOutlineStatusOnline className="ml-1 w-3.5 h-3.5" />
                        متاح للبيع
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2.5 py-1 rounded-md text-xs font-medium bg-red-50 text-red-800 dark:bg-red-900/30 dark:text-red-300 border border-red-100 dark:border-red-800">
                        <span className="w-1.5 h-1.5 rounded-full bg-red-500 ml-1"></span>
                        <HiOutlineExclamation className="ml-1 w-3.5 h-3.5" />
                        غير متاح للبيع
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
              {/* Timestamps Card */}
              <div className="mt-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 shadow p-4">
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">تاريخ المنتج</h3>
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <HiOutlineCalendar className="ml-2 w-4 h-4 text-gray-500" />
                    <span className="text-gray-600 dark:text-gray-300">تم الإضافة:</span>
                    <span className="mr-2 text-gray-800 dark:text-gray-200 font-medium">
                      {new Date(product.InsertDate).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                  
                  {product.UpdateDate && (
                    <div className="flex items-center text-sm">
                      <HiOutlineClock className="ml-2 w-4 h-4 text-gray-500" />
                      <span className="text-gray-600 dark:text-gray-300">آخر تحديث:</span>
                      <span className="mr-2 text-gray-800 dark:text-gray-200 font-medium">
                        {new Date(product.UpdateDate).toLocaleDateString('ar-SA')}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {/* Right Column - Product Details */}
            <div className="lg:col-span-3 space-y-6">
              {/* Description Card */}
              <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-100 dark:border-gray-700 shadow overflow-hidden transition-all duration-300 hover:shadow-md">
                <div className="border-b border-gray-100 dark:border-gray-700 px-5 py-3 bg-gray-50 dark:bg-gray-700 flex items-center">
                  <HiOutlineInformationCircle className="ml-2 w-5 h-5 text-gray-600 dark:text-gray-300" />
                  <h2 className="text-base font-semibold text-gray-700 dark:text-gray-200">وصف المنتج</h2>
                </div>
                
                <div className="p-5">
                  {product.Description ? (
                    <p className="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                      {product.Description}
                    </p>
                  ) : (
                    <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <p className="text-gray-500 dark:text-gray-400 text-sm">لا يوجد وصف متاح لهذا المنتج</p>
                    </div>
                  )}
                </div>
              </div>
              
              {/* Enhanced Ingredients Card */}
              {product.Ingredients && product.Ingredients.length > 0 ? (
                <div className="bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 dark:from-gray-800 dark:via-blue-900/10 dark:to-purple-900/10 rounded-2xl border border-blue-100 dark:border-blue-800/30 shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-[1.02] transform">
                  {/* Enhanced Header */}
                  <div className="relative bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-4">
                    <div className="absolute inset-0 bg-black/10"></div>
                    <div className="relative flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                          <HiOutlineViewList className="w-6 h-6 text-white" />
                        </div>
                        <div className="mr-3">
                          <h2 className="text-lg font-bold text-white">المكونات المتاحة</h2>
                          <p className="text-blue-100 text-sm">جميع المكونات المضافة لهذا المنتج</p>
                        </div>
                      </div>
                      <div className="bg-white/20 backdrop-blur-sm rounded-full px-3 py-1">
                        <span className="text-white font-semibold text-sm">
                          {product.Ingredients.length} مكون
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Content */}
                  <div className="p-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {product.Ingredients.map((ingredient, index) => (
                        <div
                          key={ingredient.IngredientID}
                          className="group relative bg-white dark:bg-gray-700 rounded-xl p-4 border border-gray-100 dark:border-gray-600 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 transform"
                          style={{
                            animationDelay: `${index * 100}ms`,
                            animation: 'fadeInUp 0.6s ease-out forwards'
                          }}
                        >
                          {/* Ingredient Icon */}
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center">
                              <div className="p-2 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg shadow-sm">
                                <HiOutlineCheck className="w-4 h-4 text-white" />
                              </div>
                              <div className="mr-3">
                                <span className="text-gray-800 dark:text-gray-200 font-medium text-sm">
                                  {ingredient.IngredientName}
                                </span>
                              </div>
                            </div>

                            {/* Ingredient Number Badge */}
                            <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                              {index + 1}
                            </div>
                          </div>

                          {/* Hover Effect */}
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                          {/* Bottom Border Animation */}
                          <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 group-hover:w-full transition-all duration-300"></div>
                        </div>
                      ))}
                    </div>

                    {/* Summary Footer */}
                    <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl border border-blue-100 dark:border-blue-800/30">
                      <div className="flex items-center justify-center text-center">
                        <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg mr-3">
                          <HiOutlineInformationCircle className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            إجمالي المكونات: <span className="font-bold text-blue-600 dark:text-blue-400">{product.Ingredients.length}</span>
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            يمكن للعملاء اختيار المكونات التي يريدونها
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-gradient-to-br from-white via-gray-50 to-gray-100 dark:from-gray-800 dark:via-gray-800 dark:to-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl">
                  {/* Empty State Header */}
                  <div className="relative bg-gradient-to-r from-gray-400 to-gray-500 px-6 py-4">
                    <div className="absolute inset-0 bg-black/10"></div>
                    <div className="relative flex items-center">
                      <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                        <HiOutlineViewList className="w-6 h-6 text-white" />
                      </div>
                      <div className="mr-3">
                        <h2 className="text-lg font-bold text-white">المكونات المتاحة</h2>
                        <p className="text-gray-100 text-sm">لا توجد مكونات مضافة حالياً</p>
                      </div>
                    </div>
                  </div>

                  {/* Empty State Content */}
                  <div className="p-8">
                    <div className="text-center">
                      <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mb-4">
                        <HiOutlineExclamationCircle className="w-8 h-8 text-gray-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                        لا توجد مكونات
                      </h3>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mb-4">
                        لم يتم إضافة أي مكونات لهذا المنتج بعد
                      </p>
                      <div className="inline-flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg text-sm font-medium">
                        <HiOutlineInformationCircle className="w-4 h-4 ml-2" />
                        يمكنك إضافة المكونات عند تعديل المنتج
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Actions */}

            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductDetails; 